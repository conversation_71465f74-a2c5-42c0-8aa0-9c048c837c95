/*
* Canabro - Medical Marijuana Dispensary HTML Template
* Main Stylesheet
*/

/* ===== GENERAL STYLES ===== */
:root {
    --primary-color: #4CAF50;
    --secondary-color: #2E7D32;
    --accent-color: #8BC34A;
    --dark-color: #333333;
    --light-color: #f8f9fa;
    --text-color: #555555;
    --border-color: #e0e0e0;
    --heading-font: 'Montserrat', sans-serif;
    --body-font: 'Poppins', sans-serif;
}

/* Page Loading Animation */
.page-loader {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 1;
    visibility: visible;
    transition: all 0.5s ease;
}

.page-loader.loaded {
    opacity: 0;
    visibility: hidden;
}

.loader-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--body-font);
    font-size: 16px;
    line-height: 1.6;
    color: var(--text-color);
    background-color: #fff;
    overflow-x: hidden;
}

h1, h2, h3, h4, h5, h6 {
    font-family: var(--heading-font);
    font-weight: 700;
    line-height: 1.3;
    margin-bottom: 15px;
    color: var(--dark-color);
}

p {
    margin-bottom: 15px;
}

a {
    color: var(--primary-color);
    text-decoration: none;
    transition: all 0.3s ease;
}

a:hover {
    color: var(--secondary-color);
    text-decoration: none;
}

ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

img {
    max-width: 100%;
    height: auto;
}

.container {
    max-width: 1200px;
    padding: 0 15px;
}

.theme-btn {
    display: inline-block;
    padding: 12px 30px;
    font-weight: 600;
    border-radius: 30px;
    text-transform: uppercase;
    font-size: 14px;
    letter-spacing: 1px;
    transition: all 0.3s ease;
    text-align: center;
}

.btn-style-one {
    background-color: var(--primary-color);
    color: white;
    border: 2px solid var(--primary-color);
}

.btn-style-one:hover {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
    color: white;
}

.btn-style-two {
    background-color: transparent;
    color: white;
    border: 2px solid white;
}

.btn-style-two:hover {
    background-color: white;
    color: var(--primary-color);
}

.sec-title {
    margin-bottom: 40px;
}

.sec-title .title {
    position: relative;
    display: block;
    font-size: 18px;
    line-height: 1.2em;
    color: var(--primary-color);
    font-weight: 500;
    margin-bottom: 15px;
    text-transform: uppercase;
}

.sec-title h2 {
    position: relative;
    display: block;
    font-size: 36px;
    line-height: 1.2em;
    color: var(--dark-color);
    font-weight: 700;
}

/* ===== PRELOADER ===== */
#preloader {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #fff;
    z-index: 9999;
}

.loader {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 80px;
    height: 80px;
}

.cannabis-loader {
    animation: spin 2s linear infinite;
    fill: var(--primary-color);
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* ===== TOP BAR ===== */
.top-bar {
    background-color: var(--dark-color);
    padding: 10px 0;
    color: white;
}

.top-bar-left .contact-info {
    display: flex;
    flex-wrap: wrap;
}

.top-bar-left .contact-info li {
    margin-right: 20px;
    font-size: 14px;
}

.top-bar-left .contact-info li i {
    margin-right: 5px;
    color: var(--primary-color);
}

.top-bar-right {
    display: flex;
    justify-content: flex-end;
    align-items: center;
}

.social-links {
    margin-right: 20px;
}

.social-links a {
    color: white;
    margin-left: 10px;
    font-size: 14px;
}

.social-links a:hover {
    color: var(--primary-color);
}

.user-links a {
    color: white;
    margin-left: 15px;
    font-size: 14px;
}

.user-links a i {
    margin-right: 5px;
    color: var(--primary-color);
}

.user-links a:hover {
    color: var(--primary-color);
}

/* ===== HEADER ===== */
.header {
    position: relative;
    background-color: white;
    padding: 0;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    z-index: 99;
    transition: all 0.3s ease, transform 0.4s ease;
}

/* Header Top Section */
.header-top {
    background-color: #f8f9fa;
    padding: 15px 0;
    border-bottom: 1px solid #e0e0e0;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.header-search {
    position: relative;
}

.header-search .search-form {
    display: flex;
    align-items: center;
    background-color: white;
    border-radius: 8px;
    overflow: hidden;
    border: 2px solid #e0e0e0;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
}

.header-search .search-form:focus-within {
    border-color: var(--primary-color);
    box-shadow: 0 2px 12px rgba(76, 175, 80, 0.15);
}

.header-search .category-select {
    border: none;
    background-color: transparent;
    padding: 12px 15px;
    font-size: 14px;
    color: #666;
    border-right: 1px solid #e0e0e0;
    outline: none;
    min-width: 150px;
}

.header-search .search-input {
    flex: 1;
    border: none;
    background-color: transparent;
    padding: 12px 15px;
    font-size: 14px;
    outline: none;
}

.header-search .search-btn {
    background-color: var(--primary-color);
    color: white;
    border: none;
    padding: 12px 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.header-search .search-btn:hover {
    background-color: var(--secondary-color);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
}

.header-right {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 15px;
}

.btn-login {
    background-color: var(--primary-color);
    color: white;
    padding: 10px 24px;
    border-radius: 6px;
    text-decoration: none;
    font-size: 14px;
    font-weight: 600;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 2px 6px rgba(76, 175, 80, 0.2);
}

.btn-login:hover {
    background-color: var(--secondary-color);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
}

.btn-store {
    background-color: var(--primary-color);
    color: white;
    padding: 10px 20px;
    border-radius: 6px;
    text-decoration: none;
    font-size: 14px;
    font-weight: 600;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 2px 6px rgba(76, 175, 80, 0.2);
    display: flex;
    align-items: center;
    gap: 8px;
}

.btn-store:hover {
    background-color: var(--secondary-color);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
}

/* Main Navigation Bar */
.main-nav-bar {
    background: linear-gradient(135deg, #DAA520 0%, #B8860B 100%);
    padding: 0;
    position: relative;
    box-shadow: 0 4px 15px rgba(218, 165, 32, 0.2);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.fixed-header {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 999;
    animation: slideDown 0.5s ease-in-out;
    background-color: rgba(255, 255, 255, 0.98);
    padding: 0;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.fixed-header .header-top {
    display: none;
}

.fixed-header .main-nav-bar {
    padding: 8px 0;
    background: linear-gradient(135deg, #DAA520 0%, #B8860B 100%);
}

.fixed-header .logo img {
    max-height: 45px;
    transition: all 0.3s ease;
}

.fixed-header .navigation > li > a {
    padding: 12px 20px;
    font-weight: 600;
    font-size: 14px;
    background-color: var(--primary-color);
    margin: 0 2px;
    border-radius: 6px 6px 0 0;
}

.fixed-header .navigation > li.current > a,
.fixed-header .navigation > li:hover > a {
    background-color: var(--secondary-color);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(46, 125, 50, 0.3);
}

.fixed-header .language-selector .form-select {
    padding: 8px 12px;
    font-size: 13px;
}

.fixed-header .mobile-nav-toggler {
    padding: 8px 10px;
    font-size: 18px;
}

@keyframes slideDown {
    0% {
        transform: translateY(-100%);
    }
    100% {
        transform: translateY(0);
    }
}

.fixed-header-padding {
    padding-top: 80px; /* Adjusted based on fixed header height */
}

.logo img {
    max-height: 60px;
}

.nav-outer {
    position: relative;
}

.main-menu {
    position: relative;
}

.navigation {
    position: relative;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    margin: 0;
    padding: 0;
}

.navigation > li {
    position: relative;
    margin: 0 3px;
    border-right: none;
}

.navigation > li:last-child {
    border-right: none;
}

.navigation > li > a {
    position: relative;
    display: block;
    font-size: 15px;
    font-weight: 600;
    color: white;
    padding: 18px 25px;
    transition: all 0.3s ease;
    text-transform: capitalize;
    text-decoration: none;
    background-color: var(--primary-color);
    letter-spacing: 0.3px;
    margin: 0 2px;
    border-radius: 6px 6px 0 0;
}

.navigation > li.current > a,
.navigation > li:hover > a {
    background-color: var(--secondary-color);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(46, 125, 50, 0.3);
}

.navigation > li.dropdown {
    position: relative;
}

/* Dropdown arrow indicator */
.navigation > li.dropdown > a::after {
    content: '\f107';
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    margin-left: 8px;
    font-size: 12px;
    transition: transform 0.3s ease;
}

.navigation > li.dropdown:hover > a::after {
    transform: rotate(180deg);
}

/* Language Selector */
.language-selector {
    display: flex;
    justify-content: flex-end;
    align-items: center;
}

.language-selector .form-select {
    background-color: rgba(255, 255, 255, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: 10px 15px;
    border-radius: 6px;
    font-size: 14px;
    outline: none;
    font-weight: 500;
    transition: all 0.3s ease;
    cursor: pointer;
}

.language-selector .form-select:hover {
    background-color: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.4);
}

.language-selector .form-select option {
    background-color: var(--primary-color);
    color: white;
    padding: 8px;
}

.navigation > li.dropdown > ul {
    position: absolute;
    left: 0;
    top: 100%;
    width: 240px;
    background-color: white;
    border-top: 3px solid var(--primary-color);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
    padding: 15px 0;
    border-radius: 0 0 12px 12px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(20px) scale(0.95);
    transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
    z-index: 100;
    backdrop-filter: blur(10px);
}

.navigation > li.dropdown:hover > ul {
    opacity: 1;
    visibility: visible;
    transform: translateY(0) scale(1);
}

.navigation > li.dropdown > ul > li {
    position: relative;
    padding: 0;
}

.navigation > li.dropdown > ul > li > a {
    position: relative;
    display: block;
    font-size: 14px;
    font-weight: 500;
    color: #333;
    padding: 14px 25px;
    border-bottom: 1px solid rgba(224, 224, 224, 0.3);
    transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
    text-decoration: none;
    overflow: hidden;
}

.navigation > li.dropdown > ul > li > a::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    width: 0;
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    transition: width 0.3s ease;
    z-index: -1;
}

.navigation > li.dropdown > ul > li:last-child > a {
    border-bottom: none;
}

.navigation > li.dropdown > ul > li:hover > a {
    color: white;
    transform: translateX(5px);
}

.navigation > li.dropdown > ul > li:hover > a::before {
    width: 100%;
}

/* Mega Menu Styles */
.navigation > li.mega-menu > ul.mega-dropdown {
    width: 500px;
    padding: 20px;
}

.navigation > li.mega-menu .dropdown-submenu {
    position: relative;
    width: 48%;
    display: inline-block;
    vertical-align: top;
    margin-right: 2%;
    margin-bottom: 20px;
}

.navigation > li.mega-menu .dropdown-submenu:last-child {
    margin-right: 0;
}

.navigation > li.mega-menu .dropdown-submenu > a {
    font-weight: 600;
    color: var(--primary-color);
    font-size: 16px;
    padding: 10px 0;
    border-bottom: 2px solid var(--primary-color);
    margin-bottom: 10px;
    text-decoration: none;
}

.navigation > li.mega-menu .dropdown-submenu > ul {
    position: static;
    width: 100%;
    background: none;
    box-shadow: none;
    border: none;
    padding: 0;
    opacity: 1;
    visibility: visible;
    transform: none;
}

.navigation > li.mega-menu .dropdown-submenu > ul > li {
    padding: 0;
}

.navigation > li.mega-menu .dropdown-submenu > ul > li > a {
    padding: 8px 0;
    font-size: 13px;
    border-bottom: 1px solid rgba(224, 224, 224, 0.3);
}

.navigation > li.mega-menu .dropdown-submenu > ul > li:hover > a {
    padding-left: 10px;
}

.header-right {
    display: flex;
    justify-content: flex-end;
    align-items: center;
}

.search-box, .cart-box {
    position: relative;
    margin-left: 20px;
}

.search-box a, .cart-box a {
    color: var(--dark-color);
    font-size: 18px;
}

.search-box a:hover, .cart-box a:hover {
    color: var(--primary-color);
}

.cart-count {
    position: absolute;
    top: -8px;
    right: -8px;
    width: 20px;
    height: 20px;
    background-color: var(--primary-color);
    color: white;
    font-size: 12px;
    font-weight: 600;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.mobile-nav-toggler {
    position: relative;
    display: none;
    font-size: 20px;
    color: white;
    cursor: pointer;
    margin-left: auto;
    background: var(--primary-color);
    padding: 10px 12px;
    border-radius: 6px;
    transition: all 0.3s ease;
    border: 1px solid var(--secondary-color);
}

.mobile-nav-toggler:hover {
    background: var(--secondary-color);
    transform: scale(1.05);
}

/* ===== MOBILE MENU ===== */
.mobile-menu {
    position: fixed;
    top: 0;
    right: -300px;
    width: 300px;
    height: 100%;
    background-color: white;
    z-index: 999;
    transition: all 0.3s ease;
    overflow-y: auto;
}

.mobile-menu.active {
    right: 0;
}

.menu-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    z-index: 998;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.mobile-menu.active + .menu-backdrop {
    opacity: 1;
    visibility: visible;
}

.close-btn {
    position: absolute;
    top: 20px;
    right: 20px;
    font-size: 20px;
    color: var(--dark-color);
    cursor: pointer;
}

.nav-logo {
    padding: 20px;
    text-align: center;
}

.nav-logo img {
    max-height: 50px;
}

.menu-box {
    position: relative;
    padding: 0;
}

.menu-outer {
    padding: 20px;
}

.menu-outer .navigation li {
    position: relative;
    display: block;
    border-bottom: 1px solid var(--border-color);
}

.menu-outer .navigation li:last-child {
    border-bottom: none;
}

.menu-outer .navigation li a {
    position: relative;
    display: block;
    padding: 10px 0;
    font-size: 16px;
    font-weight: 500;
    color: var(--dark-color);
}

.menu-outer .navigation li.current > a,
.menu-outer .navigation li:hover > a {
    color: var(--primary-color);
}

.menu-outer .navigation li.dropdown > ul {
    display: none;
    padding-left: 15px;
}

.menu-outer .navigation li.dropdown > a:after {
    content: '\f107';
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    position: absolute;
    right: 0;
    top: 10px;
}

.mobile-menu .social-links {
    position: relative;
    padding: 20px;
    text-align: center;
}

.mobile-menu .social-links a {
    margin: 0 5px;
    font-size: 16px;
}

/* ===== SEARCH POPUP ===== */
.search-popup {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.9);
    z-index: 9999;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.search-popup.active {
    opacity: 1;
    visibility: visible;
}

.close-search {
    position: absolute;
    top: 30px;
    right: 30px;
    font-size: 24px;
    color: white;
    cursor: pointer;
}

.popup-inner {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 100%;
    max-width: 600px;
    padding: 0 15px;
}

.search-form {
    position: relative;
}

.search-form .form-control {
    height: 60px;
    padding: 10px 60px 10px 20px;
    background-color: white;
    border: none;
    border-radius: 5px;
    font-size: 16px;
}

.search-form button {
    position: absolute;
    top: 0;
    right: 0;
    height: 60px;
    width: 60px;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 0 5px 5px 0;
    font-size: 18px;
    cursor: pointer;
}

/* ===== MAIN SLIDER ===== */
.main-slider {
    position: relative;
}

.main-slider .slide {
    position: relative;
    padding: 200px 0;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
}

.main-slider .slide:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
}

.main-slider .content {
    position: relative;
    max-width: 600px;
    z-index: 1;
}

.main-slider .title {
    position: relative;
    display: block;
    font-size: 18px;
    font-weight: 500;
    color: var(--primary-color);
    margin-bottom: 15px;
    opacity: 0;
    transform: translateY(50px);
    transition: all 0.5s ease;
}

.main-slider .active .title {
    opacity: 1;
    transform: translateY(0);
    transition-delay: 0.5s;
}

.main-slider h1 {
    position: relative;
    display: block;
    font-size: 60px;
    font-weight: 700;
    line-height: 1.2;
    color: white;
    margin-bottom: 20px;
    opacity: 0;
    transform: translateY(50px);
    transition: all 0.5s ease;
}

.main-slider .active h1 {
    opacity: 1;
    transform: translateY(0);
    transition-delay: 0.7s;
}

.main-slider .text {
    position: relative;
    display: block;
    font-size: 18px;
    color: white;
    margin-bottom: 30px;
    opacity: 0;
    transform: translateY(50px);
    transition: all 0.5s ease;
}

.main-slider .active .text {
    opacity: 1;
    transform: translateY(0);
    transition-delay: 0.9s;
}

.main-slider .btn-box {
    position: relative;
    opacity: 0;
    transform: translateY(50px);
    transition: all 0.5s ease;
}

.main-slider .active .btn-box {
    opacity: 1;
    transform: translateY(0);
    transition-delay: 1.1s;
}

.main-slider .btn-box .theme-btn {
    margin-right: 15px;
    margin-bottom: 10px;
}

.main-slider .owl-nav {
    position: absolute;
    top: 50%;
    width: 100%;
    transform: translateY(-50%);
}

.main-slider .owl-nav button {
    position: absolute;
    width: 50px;
    height: 50px;
    background-color: rgba(255, 255, 255, 0.3) !important;
    color: white !important;
    font-size: 24px !important;
    border-radius: 50% !important;
    transition: all 0.3s ease;
}

.main-slider .owl-nav button:hover {
    background-color: var(--primary-color) !important;
}

.main-slider .owl-nav .owl-prev {
    left: 20px;
}

.main-slider .owl-nav .owl-next {
    right: 20px;
}

.main-slider .owl-dots {
    position: absolute;
    bottom: 30px;
    left: 0;
    width: 100%;
    text-align: center;
}

.main-slider .owl-dots button {
    width: 12px;
    height: 12px;
    background-color: rgba(255, 255, 255, 0.5) !important;
    border-radius: 50%;
    margin: 0 5px;
    transition: all 0.3s ease;
}

.main-slider .owl-dots button.active {
    background-color: var(--primary-color) !important;
    width: 30px;
    border-radius: 10px;
}

/* ===== FEATURES SECTION ===== */
.features-section {
    position: relative;
    padding: 80px 0 50px;
}

.feature-block {
    position: relative;
    margin-bottom: 30px;
}

.feature-block .inner-box {
    position: relative;
    padding: 40px 30px;
    background-color: white;
    border-radius: 5px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    text-align: center;
    transition: all 0.3s ease;
}

.feature-block .inner-box:hover {
    transform: translateY(-10px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.feature-block .icon-box {
    position: relative;
    width: 80px;
    height: 80px;
    background-color: var(--light-color);
    border-radius: 50%;
    margin: 0 auto 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.feature-block .inner-box:hover .icon-box {
    background-color: var(--primary-color);
}

.feature-block .icon-box i {
    font-size: 36px;
    color: var(--primary-color);
    transition: all 0.3s ease;
}

.feature-block .inner-box:hover .icon-box i {
    color: white;
}

.feature-block h3 {
    position: relative;
    font-size: 22px;
    font-weight: 600;
    margin-bottom: 15px;
}

.feature-block .text {
    position: relative;
    font-size: 15px;
    color: var(--text-color);
}

/* ===== ABOUT SECTION ===== */
.about-section {
    position: relative;
    padding: 100px 0;
    background-color: var(--light-color);
}

.about-section .image-box {
    position: relative;
    margin-bottom: 30px;
}

.about-section .image-box .image {
    position: relative;
    margin: 0;
    border-radius: 5px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.about-section .image-box .image img {
    width: 100%;
    height: auto;
    transition: all 0.5s ease;
}

.about-section .image-box:hover .image img {
    transform: scale(1.05);
}

.about-section .content-box {
    position: relative;
}

.about-section .text {
    position: relative;
    margin-bottom: 30px;
}

.about-section .text p {
    margin-bottom: 15px;
}

.about-section .list-style-one {
    position: relative;
    margin-bottom: 30px;
}

.about-section .list-style-one li {
    position: relative;
    padding-left: 30px;
    margin-bottom: 10px;
    font-weight: 500;
}

.about-section .list-style-one li:before {
    content: '\f00c';
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    position: absolute;
    left: 0;
    top: 0;
    color: var(--primary-color);
}

/* ===== PRODUCTS SECTION ===== */
.products-section {
    position: relative;
    padding: 100px 0 70px;
}

.product-filter {
    position: relative;
    margin-bottom: 40px;
    text-align: center;
}

.product-filter ul {
    position: relative;
    display: inline-flex;
    flex-wrap: wrap;
    justify-content: center;
}

.product-filter li {
    position: relative;
    padding: 8px 20px;
    margin: 5px;
    cursor: pointer;
    border-radius: 30px;
    font-weight: 500;
    transition: all 0.3s ease;
    background-color: white;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.product-filter li.active,
.product-filter li:hover {
    background-color: var(--primary-color);
    color: white;
}

.product-item {
    position: relative;
    margin-bottom: 30px;
}

.product-box {
    position: relative;
    background-color: white;
    border-radius: 5px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.product-box:hover {
    transform: translateY(-10px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.product-box .image {
    position: relative;
    overflow: hidden;
}

.product-box .image img {
    width: 100%;
    height: 250px;
    object-fit: cover;
    transition: all 0.5s ease;
}

.product-box:hover .image img {
    transform: scale(1.1);
}

.product-box .overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.4);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: all 0.3s ease;
}

.product-box:hover .overlay {
    opacity: 1;
}

.product-box .overlay .product-links {
    position: relative;
    display: flex;
}

.product-box .overlay .product-links li {
    margin: 0 5px;
    transform: translateY(20px);
    opacity: 0;
    transition: all 0.3s ease;
}

.product-box:hover .overlay .product-links li {
    transform: translateY(0);
    opacity: 1;
}

.product-box:hover .overlay .product-links li:nth-child(1) {
    transition-delay: 0.1s;
}

.product-box:hover .overlay .product-links li:nth-child(2) {
    transition-delay: 0.2s;
}

.product-box:hover .overlay .product-links li:nth-child(3) {
    transition-delay: 0.3s;
}

.product-box .overlay .product-links li a {
    width: 40px;
    height: 40px;
    line-height: 40px;
    text-align: center;
    background-color: white;
    color: var(--dark-color);
    border-radius: 50%;
    display: block;
    transition: all 0.3s ease;
}

.product-box .overlay .product-links li a:hover {
    background-color: var(--primary-color);
    color: white;
}

.product-box .tag {
    position: absolute;
    top: 15px;
    left: 15px;
    padding: 5px 10px;
    border-radius: 3px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    color: white;
}

.product-box .tag.sale {
    background-color: #FF5722;
}

.product-box .tag.new {
    background-color: #4CAF50;
}

.product-box .tag.hot {
    background-color: #FF9800;
}

.product-box .content {
    position: relative;
    padding: 20px;
}

.product-box .category {
    font-size: 14px;
    color: var(--primary-color);
    margin-bottom: 5px;
}

.product-box h3 {
    font-size: 18px;
    margin-bottom: 10px;
}

.product-box h3 a {
    color: var(--dark-color);
}

.product-box h3 a:hover {
    color: var(--primary-color);
}

.product-box .rating {
    margin-bottom: 10px;
}

.product-box .rating i {
    color: #FFD700;
    font-size: 14px;
}

.product-box .rating span {
    color: var(--text-color);
    font-size: 14px;
    margin-left: 5px;
}

.product-box .price {
    display: flex;
    align-items: center;
}

.product-box .price .new {
    font-size: 18px;
    font-weight: 600;
    color: var(--primary-color);
}

.product-box .price .old {
    font-size: 14px;
    color: #999;
    text-decoration: line-through;
    margin-left: 10px;
}

/* ===== TESTIMONIALS SECTION ===== */
.testimonials-section {
    position: relative;
    padding: 100px 0;
    background-color: var(--light-color);
}

.testimonial-block {
    position: relative;
    margin: 15px;
}

.testimonial-block .inner-box {
    position: relative;
    background-color: white;
    border-radius: 5px;
    padding: 30px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.testimonial-block .inner-box:hover {
    transform: translateY(-10px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.testimonial-block .quote-icon {
    position: relative;
    font-size: 30px;
    color: var(--primary-color);
    margin-bottom: 15px;
    opacity: 0.3;
}

.testimonial-block .text {
    position: relative;
    font-size: 16px;
    color: var(--text-color);
    margin-bottom: 20px;
    font-style: italic;
}

.testimonial-block .info {
    position: relative;
    display: flex;
    align-items: center;
}

.testimonial-block .image {
    position: relative;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    overflow: hidden;
    margin-right: 15px;
    border: 3px solid var(--primary-color);
}

.testimonial-block .image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.testimonial-block .name {
    position: relative;
    font-size: 18px;
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 5px;
}

.testimonial-block .designation {
    position: relative;
    font-size: 14px;
    color: var(--text-color);
    margin-bottom: 5px;
}

.testimonial-block .rating {
    position: relative;
}

.testimonial-block .rating i {
    color: #FFD700;
    font-size: 12px;
}

/* ===== CTA SECTION ===== */
.cta-section {
    position: relative;
    padding: 80px 0;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    background-attachment: fixed;
}

.cta-section:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
}

.cta-section .content {
    position: relative;
    z-index: 1;
}

.cta-section h2 {
    font-size: 36px;
    color: white;
    margin-bottom: 15px;
}

.cta-section .text {
    font-size: 18px;
    color: white;
    margin-bottom: 0;
}

.cta-section .newsletter-form {
    position: relative;
    z-index: 1;
}

.cta-section .form-group {
    position: relative;
    margin-bottom: 0;
}

.cta-section .form-group input[type="email"] {
    height: 50px;
    padding: 10px 150px 10px 20px;
    background-color: white;
    border: none;
    border-radius: 30px;
    width: 100%;
}

.cta-section .form-group button {
    position: absolute;
    top: 0;
    right: 0;
    height: 50px;
    border-radius: 0 30px 30px 0;
}

/* ===== BLOG SECTION ===== */
.blog-section {
    position: relative;
    padding: 100px 0 70px;
}

.blog-block {
    position: relative;
    margin-bottom: 30px;
}

.blog-block .inner-box {
    position: relative;
    background-color: white;
    border-radius: 5px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.blog-block .inner-box:hover {
    transform: translateY(-10px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.blog-block .image {
    position: relative;
    overflow: hidden;
}

.blog-block .image img {
    width: 100%;
    height: 250px;
    object-fit: cover;
    transition: all 0.5s ease;
}

.blog-block .inner-box:hover .image img {
    transform: scale(1.1);
}

.blog-block .date {
    position: absolute;
    top: 15px;
    right: 15px;
    width: 60px;
    height: 60px;
    background-color: var(--primary-color);
    color: white;
    text-align: center;
    border-radius: 5px;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.blog-block .date span:first-child {
    font-size: 24px;
    font-weight: 700;
    line-height: 1;
}

.blog-block .date span:last-child {
    font-size: 14px;
    text-transform: uppercase;
}

.blog-block .content {
    position: relative;
    padding: 20px;
}

.blog-block .post-meta {
    position: relative;
    margin-bottom: 10px;
}

.blog-block .post-meta span {
    font-size: 14px;
    color: var(--text-color);
    margin-right: 15px;
}

.blog-block .post-meta span i {
    color: var(--primary-color);
    margin-right: 5px;
}

.blog-block h3 {
    font-size: 20px;
    margin-bottom: 10px;
}

.blog-block h3 a {
    color: var(--dark-color);
}

.blog-block h3 a:hover {
    color: var(--primary-color);
}

.blog-block .text {
    font-size: 15px;
    color: var(--text-color);
    margin-bottom: 15px;
}

.blog-block .read-more {
    font-size: 14px;
    font-weight: 500;
    color: var(--primary-color);
}

.blog-block .read-more i {
    margin-left: 5px;
    transition: all 0.3s ease;
}

.blog-block .read-more:hover i {
    margin-left: 10px;
}

/* ===== FOOTER ===== */
.main-footer {
    position: relative;
    background-color: var(--dark-color);
    color: white;
}

.widgets-section {
    position: relative;
    padding: 80px 0 30px;
}

.footer-column {
    position: relative;
    margin-bottom: 30px;
}

.footer-widget {
    position: relative;
}

.footer-widget .logo {
    position: relative;
    margin-bottom: 20px;
}

.footer-widget .logo img {
    max-height: 50px;
}

.footer-widget .text {
    position: relative;
    font-size: 14px;
    color: rgba(255, 255, 255, 0.7);
    margin-bottom: 20px;
}

.footer-widget .social-links {
    position: relative;
    display: flex;
}

.footer-widget .social-links a {
    width: 40px;
    height: 40px;
    background-color: rgba(255, 255, 255, 0.1);
    color: white;
    border-radius: 50%;
    margin-right: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.footer-widget .social-links a:hover {
    background-color: var(--primary-color);
}

.footer-widget .widget-title {
    position: relative;
    font-size: 20px;
    font-weight: 600;
    color: white;
    margin-bottom: 25px;
    padding-bottom: 10px;
}

.footer-widget .widget-title:after {
    content: '';
    position: absolute;
    left: 0;
    bottom: 0;
    width: 30px;
    height: 2px;
    background-color: var(--primary-color);
}

.footer-widget .user-links li {
    position: relative;
    margin-bottom: 10px;
}

.footer-widget .user-links li a {
    position: relative;
    font-size: 14px;
    color: rgba(255, 255, 255, 0.7);
    transition: all 0.3s ease;
}

.footer-widget .user-links li a:hover {
    color: var(--primary-color);
    padding-left: 10px;
}

.footer-widget .contact-info li {
    position: relative;
    padding-left: 30px;
    margin-bottom: 15px;
    font-size: 14px;
    color: rgba(255, 255, 255, 0.7);
}

.footer-widget .contact-info li i {
    position: absolute;
    left: 0;
    top: 5px;
    color: var(--primary-color);
}

.footer-bottom {
    position: relative;
    padding: 20px 0;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.footer-bottom .inner-container {
    position: relative;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.footer-bottom .copyright-text {
    position: relative;
    font-size: 14px;
    color: rgba(255, 255, 255, 0.7);
}

.footer-bottom .copyright-text a {
    color: var(--primary-color);
}

.footer-bottom .payment-methods {
    position: relative;
}

.footer-bottom .payment-methods img {
    max-height: 30px;
}

/* ===== AGE VERIFICATION POPUP ===== */
.age-verification {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.age-verification-content {
    position: relative;
    max-width: 500px;
    background-color: white;
    border-radius: 10px;
    padding: 40px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.age-verification-content .logo {
    position: relative;
    margin-bottom: 20px;
}

.age-verification-content .logo img {
    max-height: 60px;
}

.age-verification-content h2 {
    font-size: 28px;
    margin-bottom: 15px;
}

.age-verification-content p {
    font-size: 16px;
    margin-bottom: 25px;
}

.age-verification-content .btn-box {
    position: relative;
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-bottom: 20px;
}

.age-verification-content .disclaimer {
    position: relative;
    font-size: 14px;
    color: var(--text-color);
}

.age-verification-content .disclaimer a {
    color: var(--primary-color);
    text-decoration: underline;
}

/* ===== NEWSLETTER POPUP ===== */
.newsletter-popup {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9998;
}

.newsletter-popup-content {
    position: relative;
    max-width: 800px;
    background-color: white;
    border-radius: 10px;
    overflow: hidden;
    display: flex;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.newsletter-popup-content .close-btn {
    position: absolute;
    top: 15px;
    right: 15px;
    background: none;
    border: none;
    font-size: 20px;
    color: var(--dark-color);
    cursor: pointer;
    z-index: 1;
}

.newsletter-image {
    position: relative;
    width: 40%;
}

.newsletter-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.newsletter-content {
    position: relative;
    width: 60%;
    padding: 40px;
}

.newsletter-content h3 {
    font-size: 24px;
    margin-bottom: 15px;
}

.newsletter-content p {
    font-size: 16px;
    margin-bottom: 20px;
}

.newsletter-content .form-group {
    position: relative;
    margin-bottom: 15px;
}

.newsletter-content .form-control {
    height: 50px;
    padding: 10px 20px;
    border: 1px solid var(--border-color);
    border-radius: 5px;
    margin-bottom: 15px;
}

.newsletter-content .newsletter-note {
    font-size: 13px;
    color: #999;
}

/* ===== BACK TO TOP ===== */
.back-to-top {
    position: fixed;
    right: 20px;
    bottom: 20px;
    width: 40px;
    height: 40px;
    background-color: var(--primary-color);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 99;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.back-to-top.active {
    opacity: 1;
    visibility: visible;
}

.back-to-top:hover {
    background-color: var(--secondary-color);
    color: white;
}

/* ===== PAGE BANNER ===== */
.page-banner {
    position: relative;
    padding: 100px 0;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
}

.page-banner:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.6);
}

.page-banner .content {
    position: relative;
    text-align: center;
    z-index: 1;
}

.page-banner h1 {
    font-size: 48px;
    color: white;
    margin-bottom: 15px;
}

.page-banner .breadcrumb {
    display: flex;
    justify-content: center;
    background: none;
    padding: 0;
    margin: 0;
}

.page-banner .breadcrumb li {
    font-size: 16px;
    color: rgba(255, 255, 255, 0.7);
    padding: 0 5px;
}

.page-banner .breadcrumb li:after {
    content: '/';
    margin-left: 10px;
}

.page-banner .breadcrumb li:last-child:after {
    display: none;
}

.page-banner .breadcrumb li a {
    color: white;
}

.page-banner .breadcrumb li a:hover {
    color: var(--primary-color);
}

/* ===== BLOG PAGE SECTION ===== */
.blog-page-section {
    position: relative;
    padding: 100px 0;
}

.blog-post {
    position: relative;
    margin-bottom: 50px;
}

.blog-post .inner-box {
    position: relative;
    background-color: white;
    border-radius: 5px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.blog-post .inner-box:hover {
    transform: translateY(-10px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.blog-post .image {
    position: relative;
    overflow: hidden;
}

.blog-post .image img {
    width: 100%;
    height: 400px;
    object-fit: cover;
    transition: all 0.5s ease;
}

.blog-post .inner-box:hover .image img {
    transform: scale(1.1);
}

.blog-post .date {
    position: absolute;
    top: 15px;
    right: 15px;
    width: 60px;
    height: 60px;
    background-color: var(--primary-color);
    color: white;
    text-align: center;
    border-radius: 5px;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.blog-post .date span:first-child {
    font-size: 24px;
    font-weight: 700;
    line-height: 1;
}

.blog-post .date span:last-child {
    font-size: 14px;
    text-transform: uppercase;
}

.blog-post .content {
    position: relative;
    padding: 30px;
}

.blog-post .post-meta {
    position: relative;
    margin-bottom: 15px;
}

.blog-post .post-meta span {
    font-size: 14px;
    color: var(--text-color);
    margin-right: 20px;
}

.blog-post .post-meta span i {
    color: var(--primary-color);
    margin-right: 5px;
}

.blog-post h3 {
    font-size: 24px;
    margin-bottom: 15px;
}

.blog-post h3 a {
    color: var(--dark-color);
}

.blog-post h3 a:hover {
    color: var(--primary-color);
}

.blog-post .text {
    font-size: 16px;
    color: var(--text-color);
    margin-bottom: 20px;
}

.blog-post .read-more {
    font-size: 16px;
    font-weight: 500;
    color: var(--primary-color);
}

.blog-post .read-more i {
    margin-left: 5px;
    transition: all 0.3s ease;
}

.blog-post .read-more:hover i {
    margin-left: 10px;
}

/* Pagination */
.pagination-box {
    position: relative;
    margin-top: 30px;
    text-align: center;
}

.pagination {
    display: inline-flex;
    justify-content: center;
}

.pagination li {
    margin: 0 5px;
}

.pagination li a {
    width: 40px;
    height: 40px;
    background-color: white;
    color: var(--dark-color);
    border-radius: 5px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.pagination li.active a,
.pagination li a:hover {
    background-color: var(--primary-color);
    color: white;
}

.pagination li.disabled a {
    background-color: #f5f5f5;
    color: #999;
    cursor: not-allowed;
}

/* Sidebar */
.sidebar {
    position: relative;
}

.sidebar-widget {
    position: relative;
    margin-bottom: 40px;
    background-color: white;
    border-radius: 5px;
    padding: 30px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.sidebar-widget .widget-title {
    position: relative;
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 25px;
    padding-bottom: 10px;
}

.sidebar-widget .widget-title:after {
    content: '';
    position: absolute;
    left: 0;
    bottom: 0;
    width: 30px;
    height: 2px;
    background-color: var(--primary-color);
}

/* Search Widget */
.search-widget .form-group {
    position: relative;
    margin-bottom: 0;
}

.search-widget input[type="search"] {
    height: 50px;
    padding: 10px 60px 10px 20px;
    background-color: #f5f5f5;
    border: none;
    border-radius: 5px;
    width: 100%;
}

.search-widget button {
    position: absolute;
    top: 0;
    right: 0;
    height: 50px;
    width: 50px;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 0 5px 5px 0;
    font-size: 18px;
    cursor: pointer;
}

/* Categories Widget */
.categories-widget ul li {
    position: relative;
    padding: 10px 0;
    border-bottom: 1px solid #f0f0f0;
}

.categories-widget ul li:last-child {
    border-bottom: none;
}

.categories-widget ul li a {
    position: relative;
    display: block;
    color: var(--text-color);
    transition: all 0.3s ease;
}

.categories-widget ul li a span {
    position: absolute;
    right: 0;
    top: 0;
    background-color: #f5f5f5;
    color: var(--text-color);
    width: 25px;
    height: 25px;
    line-height: 25px;
    text-align: center;
    border-radius: 50%;
    font-size: 12px;
}

.categories-widget ul li a:hover {
    color: var(--primary-color);
    padding-left: 10px;
}

/* Recent Posts Widget */
.recent-posts-widget .post {
    position: relative;
    padding: 15px 0;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
}

.recent-posts-widget .post:first-child {
    padding-top: 0;
}

.recent-posts-widget .post:last-child {
    padding-bottom: 0;
    border-bottom: none;
}

.recent-posts-widget .thumb {
    position: relative;
    width: 80px;
    height: 80px;
    border-radius: 5px;
    overflow: hidden;
    margin-right: 15px;
}

.recent-posts-widget .thumb img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.recent-posts-widget h5 {
    font-size: 16px;
    margin-bottom: 5px;
    line-height: 1.4;
}

.recent-posts-widget h5 a {
    color: var(--dark-color);
}

.recent-posts-widget h5 a:hover {
    color: var(--primary-color);
}

.recent-posts-widget .date {
    font-size: 12px;
    color: var(--text-color);
}

.recent-posts-widget .date i {
    color: var(--primary-color);
    margin-right: 5px;
}

/* Tags Widget */
.tags-widget .tags-list {
    display: flex;
    flex-wrap: wrap;
}

.tags-widget .tags-list li {
    margin: 5px;
}

.tags-widget .tags-list li a {
    display: block;
    padding: 8px 15px;
    background-color: #f5f5f5;
    color: var(--text-color);
    border-radius: 30px;
    font-size: 14px;
    transition: all 0.3s ease;
}

.tags-widget .tags-list li a:hover {
    background-color: var(--primary-color);
    color: white;
}

/* ===== BLOG SINGLE SECTION ===== */
.blog-single-section {
    position: relative;
    padding: 100px 0;
}

.blog-detail {
    position: relative;
    margin-bottom: 50px;
}

.blog-detail .image {
    position: relative;
    margin-bottom: 30px;
    border-radius: 5px;
    overflow: hidden;
}

.blog-detail .image img {
    width: 100%;
    height: auto;
}

.blog-detail .post-meta {
    position: relative;
    margin-bottom: 20px;
    padding-bottom: 20px;
    border-bottom: 1px solid #f0f0f0;
}

.blog-detail .post-meta span {
    font-size: 14px;
    color: var(--text-color);
    margin-right: 20px;
}

.blog-detail .post-meta span i {
    color: var(--primary-color);
    margin-right: 5px;
}

.blog-detail .content {
    position: relative;
}

.blog-detail .content h3 {
    font-size: 24px;
    margin-bottom: 15px;
    margin-top: 30px;
}

.blog-detail .content p {
    margin-bottom: 20px;
}

.blog-detail .content ul {
    margin-bottom: 20px;
    padding-left: 20px;
}

.blog-detail .content ul li {
    position: relative;
    padding-left: 20px;
    margin-bottom: 10px;
    list-style-type: none;
}

.blog-detail .content ul li:before {
    content: '\f00c';
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    position: absolute;
    left: 0;
    top: 0;
    color: var(--primary-color);
}

.blog-detail .post-share {
    position: relative;
    display: flex;
    align-items: center;
    padding: 20px 0;
    margin-top: 30px;
    border-top: 1px solid #f0f0f0;
}

.blog-detail .post-share span {
    font-weight: 600;
    margin-right: 15px;
}

.blog-detail .post-share .social-links {
    display: flex;
}

.blog-detail .post-share .social-links li {
    margin-right: 10px;
}

.blog-detail .post-share .social-links li a {
    width: 40px;
    height: 40px;
    background-color: #f5f5f5;
    color: var(--dark-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.blog-detail .post-share .social-links li a:hover {
    background-color: var(--primary-color);
    color: white;
}

.blog-detail .post-tags {
    position: relative;
    display: flex;
    align-items: center;
    padding: 20px 0;
    border-top: 1px solid #f0f0f0;
}

.blog-detail .post-tags span {
    font-weight: 600;
    margin-right: 15px;
}

.blog-detail .post-tags ul {
    display: flex;
    flex-wrap: wrap;
    padding-left: 0;
}

.blog-detail .post-tags ul li {
    margin-right: 10px;
    margin-bottom: 10px;
    padding-left: 0;
}

.blog-detail .post-tags ul li:before {
    display: none;
}

.blog-detail .post-tags ul li a {
    display: block;
    padding: 8px 15px;
    background-color: #f5f5f5;
    color: var(--text-color);
    border-radius: 30px;
    font-size: 14px;
    transition: all 0.3s ease;
}

.blog-detail .post-tags ul li a:hover {
    background-color: var(--primary-color);
    color: white;
}

/* Author Box */
.author-box {
    position: relative;
    margin-bottom: 50px;
}

.author-box .inner-box {
    position: relative;
    background-color: white;
    border-radius: 5px;
    padding: 30px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    display: flex;
}

.author-box .image {
    position: relative;
    width: 100px;
    height: 100px;
    border-radius: 50%;
    overflow: hidden;
    margin-right: 20px;
}

.author-box .image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.author-box .content {
    position: relative;
    flex: 1;
}

.author-box h4 {
    font-size: 20px;
    margin-bottom: 10px;
}

.author-box p {
    font-size: 15px;
    margin-bottom: 15px;
}

.author-box .social-links {
    display: flex;
}

.author-box .social-links li {
    margin-right: 10px;
}

.author-box .social-links li a {
    width: 35px;
    height: 35px;
    background-color: #f5f5f5;
    color: var(--dark-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.author-box .social-links li a:hover {
    background-color: var(--primary-color);
    color: white;
}

/* Comments Area */
.comments-area {
    position: relative;
    margin-bottom: 50px;
}

.comments-title {
    position: relative;
    font-size: 24px;
    margin-bottom: 30px;
}

.comment-list {
    position: relative;
}

.comment {
    position: relative;
    margin-bottom: 30px;
    display: flex;
}

.comment .author-thumb {
    position: relative;
    width: 80px;
    height: 80px;
    border-radius: 50%;
    overflow: hidden;
    margin-right: 20px;
}

.comment .author-thumb img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.comment .comment-inner {
    position: relative;
    flex: 1;
    background-color: #f9f9f9;
    border-radius: 5px;
    padding: 20px;
}

.comment .comment-info {
    position: relative;
    margin-bottom: 10px;
}

.comment .comment-info h4 {
    font-size: 18px;
    margin-bottom: 5px;
}

.comment .comment-info .date {
    font-size: 12px;
    color: var(--text-color);
}

.comment .text {
    font-size: 15px;
    margin-bottom: 10px;
}

.comment .reply-btn {
    font-size: 14px;
    font-weight: 500;
    color: var(--primary-color);
}

.comment .reply-btn:hover {
    text-decoration: underline;
}

/* Comment Form */
.comment-form {
    position: relative;
}

.comment-form h3 {
    font-size: 24px;
    margin-bottom: 30px;
}

.comment-form .form-group {
    position: relative;
    margin-bottom: 20px;
}

.comment-form input[type="text"],
.comment-form input[type="email"],
.comment-form textarea {
    width: 100%;
    height: 50px;
    padding: 10px 20px;
    background-color: #f5f5f5;
    border: none;
    border-radius: 5px;
    font-size: 15px;
}

.comment-form textarea {
    height: 150px;
    resize: none;
    padding: 15px 20px;
}

.comment-form button {
    padding: 12px 30px;
}

/* ===== RESPONSIVE STYLES ===== */
@media (max-width: 1199px) {
    .main-slider .slide {
        padding: 150px 0;
    }

    .main-slider h1 {
        font-size: 48px;
    }
}

@media (max-width: 991px) {
    .navigation {
        display: none;
    }

    .mobile-nav-toggler {
        display: block;
        color: white;
    }

    .header-search .search-form {
        flex-direction: column;
        gap: 10px;
    }

    .header-search .category-select {
        min-width: 100%;
        border-right: none;
        border-bottom: 1px solid #e0e0e0;
    }

    .header-right {
        flex-direction: column;
        gap: 10px;
    }

    .main-slider .slide {
        padding: 120px 0;
    }

    .main-slider h1 {
        font-size: 36px;
    }

    .main-slider .content {
        max-width: 500px;
    }

    .newsletter-popup-content {
        max-width: 90%;
        flex-direction: column;
    }

    .newsletter-image {
        width: 100%;
        height: 200px;
    }

    .newsletter-content {
        width: 100%;
    }
}

@media (max-width: 767px) {
    .top-bar-left .contact-info {
        justify-content: center;
        margin-bottom: 10px;
    }

    .top-bar-right {
        justify-content: center;
    }

    .main-slider .slide {
        padding: 100px 0;
    }

    .main-slider h1 {
        font-size: 30px;
    }

    .main-slider .text {
        font-size: 16px;
    }

    .sec-title h2 {
        font-size: 28px;
    }

    .cta-section h2 {
        font-size: 28px;
        margin-bottom: 20px;
    }

    .cta-section .newsletter-form {
        margin-top: 30px;
    }

    .footer-bottom .inner-container {
        flex-direction: column;
        text-align: center;
    }

    .footer-bottom .copyright-text {
        margin-bottom: 10px;
    }

    /* Blog Page Responsive */
    .page-banner h1 {
        font-size: 36px;
    }

    .blog-post .image img {
        height: 300px;
    }

    .blog-post .content {
        padding: 20px;
    }

    .blog-post h3 {
        font-size: 20px;
    }

    .sidebar {
        margin-top: 50px;
    }

    /* Blog Single Responsive */
    .blog-detail .content h3 {
        font-size: 22px;
    }

    .blog-detail .post-share,
    .blog-detail .post-tags {
        flex-direction: column;
        align-items: flex-start;
    }

    .blog-detail .post-share span,
    .blog-detail .post-tags span {
        margin-bottom: 10px;
    }

    .author-box .inner-box {
        flex-direction: column;
        text-align: center;
    }

    .author-box .image {
        margin: 0 auto 20px;
    }

    .author-box .social-links {
        justify-content: center;
    }

    .comment {
        flex-direction: column;
    }

    .comment .author-thumb {
        margin: 0 auto 20px;
    }
}

@media (max-width: 575px) {
    .top-bar-left .contact-info li {
        margin-right: 10px;
        font-size: 12px;
    }

    .social-links, .user-links {
        margin-right: 10px;
    }

    .social-links a, .user-links a {
        margin-left: 8px;
        font-size: 12px;
    }

    .main-slider .slide {
        padding: 80px 0;
    }

    .main-slider h1 {
        font-size: 24px;
    }

    .main-slider .text {
        font-size: 14px;
    }

    .theme-btn {
        padding: 10px 20px;
        font-size: 12px;
    }

    .age-verification-content {
        max-width: 90%;
        padding: 30px 20px;
    }

    .age-verification-content .btn-box {
        flex-direction: column;
    }

    /* Blog Page Responsive */
    .page-banner {
        padding: 80px 0;
    }

    .page-banner h1 {
        font-size: 28px;
    }

    .blog-page-section {
        padding: 70px 0;
    }

    .blog-post .image img {
        height: 220px;
    }

    .blog-post .post-meta span {
        display: block;
        margin-bottom: 5px;
    }

    .blog-post h3 {
        font-size: 18px;
    }

    .sidebar-widget {
        padding: 20px;
    }

    .pagination li a {
        width: 35px;
        height: 35px;
    }

    /* Blog Single Responsive */
    .blog-single-section {
        padding: 70px 0;
    }

    .blog-detail .content h3 {
        font-size: 20px;
    }

    .blog-detail .post-meta span {
        display: block;
        margin-bottom: 5px;
    }

    .author-box .inner-box {
        padding: 20px;
    }

    .author-box .image {
        width: 80px;
        height: 80px;
    }

    .author-box h4 {
        font-size: 18px;
    }

    .comments-title {
        font-size: 20px;
    }

    .comment .comment-inner {
        padding: 15px;
    }

    .comment .comment-info h4 {
        font-size: 16px;
    }

    .comment-form h3 {
        font-size: 20px;
    }
}
